version: '3.8'

services:
  # Development configuration for NestJS Application
  nest-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: nest-crawler-app-dev
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    environment:
      NODE_ENV: development
      PORT: 3000
      # Development-specific environment variables
      CRAWL_START_URLS: "[https://example.com,https://httpbin.org]"
      CRAWL_CONCURRENCY: 3
      LOG_LEVEL: debug
    volumes:
      # Source code hot reload
      - .:/app
      - /app/node_modules
      - /app/dist
      # Persist crawled data in development
      - ./storage:/app/storage
      # Persist node_modules for faster rebuilds
      - node_modules_cache:/app/node_modules
    command: npm run start:debug
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for development (with exposed port for debugging)
  redis:
    container_name: nest-crawler-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    environment:
      REDIS_PASSWORD: ""  # No password for development
    command: redis-server --appendonly yes --save 60 1000

  # Optional: Redis Commander for Redis GUI management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: nest-crawler-redis-commander
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    depends_on:
      - redis
    restart: unless-stopped

  # Optional: Adminer for database management (if you add a database later)
  # adminer:
  #   image: adminer:latest
  #   container_name: nest-crawler-adminer
  #   ports:
  #     - "8080:8080"
  #   restart: unless-stopped

volumes:
  node_modules_cache:
    driver: local
  redis_dev_data:
    driver: local

networks:
  default:
    name: nest-crawler-dev-network
